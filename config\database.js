const mongoose = require("mongoose");
require("dotenv").config();

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error("Database connection error:", error.message);
    console.log(
      "⚠️  Running without database connection. Some features may not work."
    );
    // Don't exit the process, allow the app to run without DB for demo purposes
  }
};

module.exports = connectDB;
