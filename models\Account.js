const mongoose = require("mongoose");
const { v4: uuidv4 } = require("uuid");
const crypto = require("crypto");

const accountSchema = new mongoose.Schema(
  {
    accountId: {
      type: String,
      unique: true,
      required: true,
      default: () => uuidv4(),
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    accountName: {
      type: String,
      required: true,
      trim: true,
    },
    appSecretToken: {
      type: String,
      unique: true,
      default: () => crypto.randomBytes(32).toString("hex"),
    },
    website: {
      type: String,
      trim: true,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes are already created by unique: true in schema

// Pre-save middleware to ensure unique tokens
accountSchema.pre("save", async function (next) {
  if (this.isNew && !this.appSecretToken) {
    this.appSecretToken = crypto.randomBytes(32).toString("hex");
  }
  next();
});

module.exports = mongoose.model("Account", accountSchema);
