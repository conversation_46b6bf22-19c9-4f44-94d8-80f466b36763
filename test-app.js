// Simple test script to verify the application works
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testApplication() {
  console.log('🧪 Testing Data Pusher Application...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${BASE_URL}/`);
    console.log('✅ Health check passed:', healthResponse.data.message);

    // Test 2: Create account
    console.log('\n2. Creating test account...');
    const accountData = {
      email: '<EMAIL>',
      accountName: 'Test Company',
      website: 'https://test.com'
    };
    
    const accountResponse = await axios.post(`${BASE_URL}/api/accounts`, accountData);
    const account = accountResponse.data.data;
    console.log('✅ Account created:', account.accountName);
    console.log('   Account ID:', account.accountId);
    console.log('   App Secret Token:', account.appSecretToken);

    // Test 3: Create destination
    console.log('\n3. Creating test destination...');
    const destinationData = {
      accountId: account.accountId,
      url: 'https://httpbin.org/post',
      httpMethod: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DataPusher/1.0',
        'X-Test-Header': 'test-value'
      }
    };

    const destinationResponse = await axios.post(`${BASE_URL}/api/destinations`, destinationData);
    const destination = destinationResponse.data.data;
    console.log('✅ Destination created:', destination.url);

    // Test 4: Send data
    console.log('\n4. Sending test data...');
    const testData = {
      userId: 123,
      action: 'test_action',
      timestamp: new Date().toISOString(),
      data: {
        message: 'Hello from Data Pusher!',
        test: true
      }
    };

    const dataResponse = await axios.post(`${BASE_URL}/server/incoming_data`, testData, {
      headers: {
        'Content-Type': 'application/json',
        'CL-X-TOKEN': account.appSecretToken
      }
    });

    console.log('✅ Data sent successfully!');
    console.log('   Destinations sent:', dataResponse.data.destinationsSent);
    console.log('   Results:', dataResponse.data.results);

    // Test 5: Get destinations for account
    console.log('\n5. Getting destinations for account...');
    const accountDestinations = await axios.get(`${BASE_URL}/api/destinations/account/${account.accountId}`);
    console.log('✅ Found', accountDestinations.data.count, 'destinations for account');

    // Cleanup
    console.log('\n6. Cleaning up test data...');
    await axios.delete(`${BASE_URL}/api/accounts/${account.accountId}`);
    console.log('✅ Test account and destinations deleted');

    console.log('\n🎉 All tests passed! Application is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testApplication();
}

module.exports = testApplication;
