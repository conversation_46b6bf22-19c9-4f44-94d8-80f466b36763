# Data Pusher Setup Guide

## Quick Start

### 1. Prerequisites
- Node.js (v18 or higher)
- MongoDB (local installation or cloud instance)
- Git

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd data-pusher

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
```

### 3. MongoDB Setup

#### Option A: Local MongoDB
1. Install MongoDB Community Edition from https://www.mongodb.com/try/download/community
2. Start MongoDB service:
   - Windows: `net start MongoDB`
   - macOS: `brew services start mongodb-community`
   - Linux: `sudo systemctl start mongod`

#### Option B: MongoDB Atlas (Cloud)
1. Create a free account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Get your connection string
4. Update `.env` file with your connection string:
   ```
   MONGODB_URI=mongodb+srv://username:<EMAIL>/data-pusher?retryWrites=true&w=majority
   ```

### 4. Start the Application

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

The server will start on http://localhost:3000

### 5. Test the Application

#### Using curl:
```bash
# Health check
curl http://localhost:3000

# Create an account
curl -X POST http://localhost:3000/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "accountName": "Test Company",
    "website": "https://test.com"
  }'
```

#### Using the test script:
```bash
node test-app.js
```

## API Documentation

### Base URL
```
http://localhost:3000
```

### Authentication
For data handling endpoints, include the app secret token in the `CL-X-TOKEN` header.

### Key Endpoints

#### Account Management
- `POST /api/accounts` - Create account
- `GET /api/accounts` - List accounts
- `GET /api/accounts/:id` - Get account
- `PUT /api/accounts/:id` - Update account
- `DELETE /api/accounts/:id` - Delete account

#### Destination Management
- `POST /api/destinations` - Create destination
- `GET /api/destinations/account/:accountId` - Get account destinations
- `PUT /api/destinations/:id` - Update destination
- `DELETE /api/destinations/:id` - Delete destination

#### Data Handler
- `POST /server/incoming_data` - Send data to destinations (requires CL-X-TOKEN header)

## Sample Usage

### 1. Create Account
```bash
curl -X POST http://localhost:3000/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "accountName": "My Company"
  }'
```

Response:
```json
{
  "success": true,
  "data": {
    "accountId": "uuid-here",
    "email": "<EMAIL>",
    "accountName": "My Company",
    "appSecretToken": "generated-token-here"
  }
}
```

### 2. Create Destination
```bash
curl -X POST http://localhost:3000/api/destinations \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "your-account-id",
    "url": "https://webhook.site/unique-id",
    "httpMethod": "POST",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer token123"
    }
  }'
```

### 3. Send Data
```bash
curl -X POST http://localhost:3000/server/incoming_data \
  -H "Content-Type: application/json" \
  -H "CL-X-TOKEN: your-app-secret-token" \
  -d '{
    "userId": 123,
    "action": "user_updated",
    "data": {
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }'
```

## Testing with Sample APIs

The `sample-apis/` directory contains:
- `README.md` - Detailed API documentation
- `sample-requests.http` - HTTP requests for VS Code REST Client
- `postman-collection.json` - Postman collection for testing

## Troubleshooting

### MongoDB Connection Issues
- Ensure MongoDB is running
- Check connection string in `.env`
- For local MongoDB, try: `mongodb://127.0.0.1:27017/data-pusher`

### Port Already in Use
- Change PORT in `.env` file
- Or kill the process using port 3000: `npx kill-port 3000`

### Dependencies Issues
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## Production Deployment

### Environment Variables
Set these in production:
```
NODE_ENV=production
PORT=3000
MONGODB_URI=your-production-mongodb-uri
```

### PM2 (Process Manager)
```bash
npm install -g pm2
pm2 start server.js --name "data-pusher"
pm2 startup
pm2 save
```

### Docker (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["node", "server.js"]
```

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the API documentation in `sample-apis/README.md`
3. Test with the provided sample requests
4. Check server logs for error messages
